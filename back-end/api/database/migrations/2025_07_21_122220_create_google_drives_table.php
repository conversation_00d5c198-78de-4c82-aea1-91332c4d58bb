<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('google_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('google_account_id')->references('id')->on('google_accounts')->cascadeOnDelete();
            $table->string('external_id');
            $table->string('type');
            $table->string('name');
            $table->json('capabilities');
            $table->longText('url');
            $table->dateTime('external_created_at');
            $table->dateTime('external_updated_at');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('google_files');
    }
};
